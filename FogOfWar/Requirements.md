# FogOfWar — Unified Requirements Specification  
Target OS iOS 18 · Primary Devices iPhone 16 family (universal iPad support)

──────────────────────────────────────────────────────────────────────────────
0 EXECUTIVE SUMMARY  
Provide a single-source, iCloud-synced field-mapping tool that:  
• Displays high-resolution raster overlays.  
• Records coloured GPS tracks.  
• Allows user-defined photo markers with custom categories.  
• Paints colour-coded areas of any common shape (polygon · rectangle · circle).  

Design pillars Single source of truth · Battery-friendly · Cloud-private · Offline-robust · Accessible.  
Critical path Map core ➜ Overlay ➜ Track ➜ Marker ➜ Area ➜ Composite layers.

──────────────────────────────────────────────────────────────────────────────
1 PLATFORM & TOOLCHAIN  
Xcode 17+ · Swift 5.10 (async/await, Observation) · Deployment iOS 18.0  
Frameworks MapKit · SwiftData / Core Data · Core Location · CloudKit · PhotosUI · Combine  
CI GitHub Actions → `xcodebuild test` on generic iOS device

──────────────────────────────────────────────────────────────────────────────
2 DATA MODEL — NO ARRAY ATTRIBUTES  
All to-many relationships live on the *child* side.  
“?” = optional · [ ] = empty inverse placeholder (needed only for clarity).

    // Overlay raster + transform
    @Model final class Overlay {
        @Attribute(.unique) var id: UUID = .init()
        var name: String
        var overlayDescription: String?            // long text
        var imageLocalIdentifier: String?          // PHAsset-local ID
        var imageURL: URL?                         // on-disk fallback
        var centerLatitude: Double
        var centerLongitude: Double
        var baselineFit: Double                    // metres / px at import
        var relativeScale: Double
        var rotation: Double                       // radians CW
        var opacity: Double = 1.0                  // 0‒1
        var zOrder: Int32 = 0
        var isVisible: Bool = true
        var forwardMatrix: Data?                   // 9 × Double, row-major
        var createdAt: Date = .now
        var updatedAt: Date = .now
        @Transient var controlPoints: [ ]          // inverse of OverlayControlPoint.overlay
    }

    // Georeference control point (pixel ↔ geo)
    @Model final class OverlayControlPoint {
        var overlay: Overlay?                      // to-one parent
        var pixelX: Double
        var pixelY: Double
        var latitude: Double
        var longitude: Double
        var index: Int16                           // order 0‒3
    }

    // GPS track session
    @Model final class Track {
        @Attribute(.unique) var id: UUID = .init()
        var name: String
        var trackDescription: String?
        var colourHex: String
        var startedAt: Date
        var endedAt: Date?
        var lengthMetres: Double = 0
        var durationSeconds: Double = 0
        var isVisible: Bool = true
        @Transient var points: [ ]                 // inverse of TrackPoint.track
    }

    // Single GPS fix
    @Model final class TrackPoint {
        var track: Track?                          // to-one parent
        var latitude: Double
        var longitude: Double
        var altitude: Double?
        var horizontalAccuracy: Double
        var timestamp: Date
    }

    // User-defined marker category (e.g. “Tree”, “Nest”)
    @Model final class MarkerCategory {
        @Attribute(.unique) var id: UUID = .init()
        var name: String
        var iconName: String                       // SF Symbol
        var createdAt: Date = .now
        @Transient var markers: [ ]                // inverse of Marker.category
    }

    // Photo marker
    @Model final class Marker {
        @Attribute(.unique) var id: UUID = .init()
        var name: String
        var markerDescription: String?
        var category: MarkerCategory?              // to-one
        var iconNameOverride: String?              // custom SF Symbol
        var photoAssetID: String                   // PHAsset ID
        var latitude: Double
        var longitude: Double
        var isVisible: Bool = true
        var createdAt: Date = .now
    }

    // Colour-filled area of coverage
    @Model final class Area {
        @Attribute(.unique) var id: UUID = .init()
        var name: String
        var areaDescription: String?
        var colourHex: String
        var opacity: Double                        // 0.1…0.9
        var shapeType: String = "polygon"          // "polygon" | "rectangle" | "circle"
        var radiusMetres: Double?                  // used when shapeType == "circle"
        var isVisible: Bool = true
        var createdAt: Date = .now
        @Transient var vertices: [ ]               // inverse of AreaVertex.area
    }

    // Ordered vertex of an Area polygon / rectangle
    @Model final class AreaVertex {
        var area: Area?                            // to-one parent
        var latitude: Double
        var longitude: Double
        var index: Int32                           // winding order
    }

──────────────────────────────────────────────────────────────────────────────
3 CORE COMPONENTS  
Matrix3x3 · OverlayTransform (DLT) · ImageOverlayRenderer  
LocationManager (AsyncStream) · TrackThumbnailGenerator  
MarkerAnnotation (clusterable) · AreaOverlayRenderer

──────────────────────────────────────────────────────────────────────────────
4 ALGORITHMS & INVARIANTS  
• **Overlay** `computeOverlayFrame(overlay,mapView)` must agree between editor & renderer ≤ 2 px.  
• **Track** Store point if `distance ≥ 5 m OR elapsed ≥ 1 s`, and `horizontalAccuracy ≤ 10 m`; compute length with Haversine; battery ≤ 5 % / h.  
• **Marker** EXIF GPS auto-place; else lat/lon grid-snap (0.005°).  
• **Area** `shapeType` governs renderer:  
  – *polygon* or *rectangle* → ordered `AreaVertex` list.  
  – *circle* → center = first vertex; radius = `radiusMetres`.  
• **Photos** Remain in Photos library; only `photoAssetID` synced via CloudKit.  
• **Conflict** CloudKit last-write-wins; local tombstones for deletes.

──────────────────────────────────────────────────────────────────────────────
5 PHASED ROADMAP (sequential — do not overlap)

PH 0 Project Skeleton & CI  
    • Empty SwiftUI app, bundle IDs, CloudKit entitlements, GitHub Actions build/test.  

PH 1 Map Core  
    • Single `MKMapView` via UIViewRepresentable; device location; 60 FPS pan/zoom.  

PH 2 Overlay CRUD & Georeference  
    • Entities Overlay + OverlayControlPoint; image import & down-sample; live edit sliders; `ImageOverlayRenderer`; Overlay List.  

PH 3 Track Recording  
    • Entities Track + TrackPoint; `LocationManager` stream; real-time polyline; length/duration calc; Track thumbnails; GPX export.  

PH 4 Marker Management  
    • Entity MarkerCategory (Settings CRUD) + Marker; PHPicker import; EXIF placement; grid-snap edit; icon clustering; Marker List.  

PH 5 Area Painting  
    • Entities Area + AreaVertex; shape choice (polygon · rectangle · circle); drawing gestures; AreaOverlayRenderer; Area List.  

PH 6 Composite Layer Panel  
    • Side drawer with global switches & per-layer opacity sliders; z-order logic.  

PH 7 Export / Import  
    • `.fogofwar` bundle (JSON + overlay images + GPX + thumbnails); snapshot export; import coordinator.  

PH 8 Sync Hardening & QA  
    • Stress test CloudKit, offline queue, conflict UI, diagnostics.  

PH 9 Polish & TestFlight  
    • Accessibility, energy profiling, screenshots, App Store metadata.

──────────────────────────────────────────────────────────────────────────────
6 LLM EXECUTION CHECKLIST (mark ✅ when complete)

1️⃣ Bootstrap Xcode project, bundle IDs, CloudKit, CI workflow  
2️⃣ Build MapViewRepresentable + MapViewModel; verify location & panning  
3️⃣ Define SwiftData entities exactly as **Section 2** (Overlay … AreaVertex)  
4️⃣ Implement image import pipeline (PHPicker → down-sample → file / PHAsset ID)  
5️⃣ OverlayEditView with sliders, live preview, OverlayControlPoint CRUD  
6️⃣ `computeOverlayFrame` + `ImageOverlayRenderer`; render overlay on map  
7️⃣ Overlay List UI (visibility, reorder, delete)  
8️⃣ LocationManager stream; persist TrackPoint during recording  
9️⃣ Track length/duration calculation; TrackThumbnailGenerator  
🔟 Track List with visibility toggles, thumbnails  
1️⃣1️⃣ GPX export for selected tracks  
1️⃣2️⃣ Settings UI for MarkerCategory CRUD  
1️⃣3️⃣ Marker import with EXIF auto-placement; grid-snap edit; MarkerAnnotation clustering  
1️⃣4️⃣ Marker List (category filter, visibility)  
1️⃣5️⃣ Area drawing tool (polygon · rectangle · circle); store AreaVertex / radiusMetres  
1️⃣6️⃣ AreaOverlayRenderer; Area List (colour, opacity, visibility)  
1️⃣7️⃣ Layer panel with global opacity sliders, z-order logic  
1️⃣8️⃣ ExportCoordinator (.fogofwar bundle, snapshot) & ImportCoordinator  
1️⃣9️⃣ NSPersistentCloudKitContainer conflict handling; sync diagnostics UI  
2️⃣0️⃣ Unit + UI tests; VoiceOver & Dynamic Type audit; energy ≤ 5 % / h  
2️⃣1️⃣ App Store screenshots, metadata, submit TestFlight build

──────────────────────────────────────────────────────────────────────────────
END OF SPECIFICATION