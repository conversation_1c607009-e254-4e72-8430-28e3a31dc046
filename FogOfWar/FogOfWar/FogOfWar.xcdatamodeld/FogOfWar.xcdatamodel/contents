<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="1" systemVersion="11A491" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="true" userDefinedModelVersionIdentifier="">
    <entity name="Overlay" representedClassName="Overlay" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO" optional="NO"/>
        <attribute name="name" attributeType="String" optional="NO"/>
        <attribute name="overlayDescription" attributeType="String" optional="YES"/>
        <attribute name="imageLocalIdentifier" attributeType="String" optional="YES"/>
        <attribute name="imageURL" attributeType="URI" optional="YES"/>
        <attribute name="centerLatitude" attributeType="Double" optional="NO"/>
        <attribute name="centerLongitude" attributeType="Double" optional="NO"/>
        <attribute name="baselineFit" attributeType="Double" optional="NO"/>
        <attribute name="relativeScale" attributeType="Double" optional="NO"/>
        <attribute name="rotation" attributeType="Double" optional="NO"/>
        <attribute name="opacity" attributeType="Double" optional="NO" defaultValueString="1"/>
        <attribute name="zOrder" attributeType="Integer 32" optional="NO" defaultValueString="0"/>
        <attribute name="isVisible" attributeType="Boolean" optional="NO" defaultValueString="YES"/>
        <attribute name="forwardMatrix" attributeType="Binary" optional="YES"/>
        <attribute name="createdAt" attributeType="Date" optional="NO"/>
        <attribute name="updatedAt" attributeType="Date" optional="NO"/>
    </entity>
    <entity name="OverlayControlPoint" representedClassName="OverlayControlPoint" syncable="YES" codeGenerationType="class">
        <attribute name="pixelX" attributeType="Double" optional="NO"/>
        <attribute name="pixelY" attributeType="Double" optional="NO"/>
        <attribute name="latitude" attributeType="Double" optional="NO"/>
        <attribute name="longitude" attributeType="Double" optional="NO"/>
        <attribute name="index" attributeType="Integer 16" optional="NO"/>
        <relationship name="overlay" optional="YES" toMany="NO" deletionRule="Nullify" destinationEntity="Overlay"/>
    </entity>
    <elements>
        <element name="Overlay" positionX="0" positionY="0" width="128" height="44"/>
        <element name="OverlayControlPoint" positionX="200" positionY="0" width="128" height="44"/>
    </elements>
</model>
