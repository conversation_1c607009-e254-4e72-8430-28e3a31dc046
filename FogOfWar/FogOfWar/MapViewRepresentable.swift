import SwiftUI
import MapKit

struct MapViewRepresentable: UIViewRepresentable {
    var overlays: [Overlay]

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView(frame: .zero)
        mapView.showsUserLocation = true
        mapView.delegate = context.coordinator
        return mapView
    }

    func updateUIView(_ uiView: MKMapView, context: Context) {
        uiView.removeOverlays(uiView.overlays)
        for overlay in overlays where overlay.isVisible {
            if let url = overlay.imageURL {
                let mapOverlay = ImageOverlay(overlay: overlay, imageURL: url)
                uiView.addOverlay(mapOverlay)
            }
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator()
    }

    class Coordinator: NSObject, MKMapViewDelegate {
        func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
            if let imageOverlay = overlay as? ImageOverlay {
                return ImageOverlayRenderer(overlay: imageOverlay)
            }
            return MKO<PERSON>layRenderer(overlay: overlay)
        }
    }
}

class ImageOverlay: NSObject, MKOverlay {
    let overlayData: Overlay
    let imageURL: URL

    init(overlay: Overlay, imageURL: URL) {
        self.overlayData = overlay
        self.imageURL = imageURL
    }

    var coordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: overlayData.centerLatitude, longitude: overlayData.centerLongitude)
    }

    var boundingMapRect: MKMapRect {
        let size = MKMapSize(width: overlayData.relativeScale, height: overlayData.relativeScale)
        let origin = MKMapPoint(coordinate).applying(CGAffineTransform(translationX: -size.width/2, y: -size.height/2))
        return MKMapRect(origin: origin, size: size)
    }
}

class ImageOverlayRenderer: MKOverlayRenderer {
    var image: UIImage?

    override init(overlay: MKOverlay) {
        if let imageOverlay = overlay as? ImageOverlay {
            self.image = UIImage(contentsOfFile: imageOverlay.imageURL.path)
        }
        super.init(overlay: overlay)
    }

    override func draw(_ mapRect: MKMapRect, zoomScale: MKZoomScale, in context: CGContext) {
        guard let image = image else { return }
        let rect = self.rect(for: overlay.boundingMapRect)
        context.saveGState()
        context.translateBy(x: rect.midX, y: rect.midY)
        if let imageOverlay = overlay as? ImageOverlay {
            context.rotate(by: imageOverlay.overlayData.rotation)
            context.setAlpha(CGFloat(imageOverlay.overlayData.opacity))
        }
        context.translateBy(x: -rect.size.width/2, y: -rect.size.height/2)
        context.draw(image.cgImage!, in: CGRect(origin: .zero, size: rect.size))
        context.restoreGState()
    }
}
