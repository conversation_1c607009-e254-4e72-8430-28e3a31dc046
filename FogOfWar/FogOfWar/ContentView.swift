import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext

    var body: some View {
        NavigationStack {
            ZStack(alignment: .bottomTrailing) {
                MapViewContent()
                    .ignoresSafeArea()
                OverlayManager()
                    .padding()
            }
            .toolbar {
                NavigationLink("Overlays") {
                    OverlayListView()
                }
            }
        }
    }
}

#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
